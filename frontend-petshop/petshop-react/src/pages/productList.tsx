// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useEffect } from 'react';


const ProductList: React.FC = () => {
  const [cartCount, setCartCount] = useState<number>(0);
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedPetType, setSelectedPetType] = useState<string[]>(["dog", "cat", "other"]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000000]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [sortOption, setSortOption] = useState<string>("newest");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(12);

  const addToCart = () => {
    setCartCount(prevCount => prevCount + 1);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setCurrentPage(1);
  };

  const handlePetTypeChange = (type: string) => {
    if (selectedPetType.includes(type)) {
      setSelectedPetType(selectedPetType.filter(item => item !== type));
    } else {
      setSelectedPetType([...selectedPetType, type]);
    }
    setCurrentPage(1);
  };

  const handleBrandChange = (brand: string) => {
    if (selectedBrands.includes(brand)) {
      setSelectedBrands(selectedBrands.filter(item => item !== brand));
    } else {
      setSelectedBrands([...selectedBrands, brand]);
    }
    setCurrentPage(1);
  };

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = parseInt(e.target.value);
    if (index === 0) {
      setPriceRange([value, priceRange[1]]);
    } else {
      setPriceRange([priceRange[0], value]);
    }
    setCurrentPage(1);
  };

  const handleSortChange = (option: string) => {
    setSortOption(option);
  };

  const handleViewModeChange = (mode: "grid" | "list") => {
    setViewMode(mode);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(parseInt(e.target.value));
    setCurrentPage(1);
  };

  // Brands data
  const brands = [
    "Royal Canin", "Whiskas", "Pedigree", "Purina", "Hill's", 
    "Kong", "Furminator", "PetWell", "Petstages", "Hartz"
  ];

  // Products data
  const products = [
    {
      id: 1,
      name: "Royal Canin Medium Adult",
      price: 350000,
      originalPrice: 400000,
      category: "food",
      petType: "dog",
      brand: "Royal Canin",
      image: "https://readdy.ai/api/search-image?query=Premium%20dog%20food%20bag%20with%20high-quality%20kibble%20spilling%20out%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20of%20the%20dog%20food%20with%20professional%20product%20photography%20style&width=300&height=300&seq=101&orientation=squarish"
    },
    {
      id: 2,
      name: "Whiskas Cá Ngừ Sốt",
      price: 25000,
      originalPrice: 30000,
      category: "food",
      petType: "cat",
      brand: "Whiskas",
      image: "https://readdy.ai/api/search-image?query=Premium%20cat%20food%20with%20salmon%20chunks%20in%20gravy%20in%20an%20elegant%20bowl%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20of%20the%20wet%20cat%20food%20with%20professional%20food%20photography%20style&width=300&height=300&seq=102&orientation=squarish"
    },
    {
      id: 3,
      name: "Vòng Cổ Da Cao Cấp",
      price: 120000,
      originalPrice: 150000,
      category: "accessories",
      petType: "dog",
      brand: "Kong",
      image: "https://readdy.ai/api/search-image?query=Stylish%20blue%20leather%20dog%20collar%20with%20metal%20buckle%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20and%20craftsmanship%20with%20professional%20product%20photography%20style&width=300&height=300&seq=103&orientation=squarish"
    },
    {
      id: 4,
      name: "Lược Chải Lông Furminator",
      price: 280000,
      originalPrice: 320000,
      category: "healthcare",
      petType: "dog",
      brand: "Furminator",
      image: "https://readdy.ai/api/search-image?query=Pet%20grooming%20brush%20with%20soft%20bristles%20and%20ergonomic%20handle%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20and%20design%20with%20professional%20product%20photography%20style&width=300&height=300&seq=104&orientation=squarish"
    },
    {
      id: 5,
      name: "Đồ Chơi Mèo Interactivo",
      price: 85000,
      originalPrice: 100000,
      category: "accessories",
      petType: "cat",
      brand: "Petstages",
      image: "https://readdy.ai/api/search-image?query=Colorful%20interactive%20cat%20toy%20with%20feathers%20and%20bells%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20and%20playful%20design%20with%20professional%20product%20photography%20style&width=300&height=300&seq=105&orientation=squarish"
    },
    {
      id: 6,
      name: "Giường Chó Comfort Plus",
      price: 450000,
      originalPrice: 550000,
      category: "accessories",
      petType: "dog",
      brand: "Kong",
      image: "https://readdy.ai/api/search-image?query=Comfortable%20dog%20bed%20with%20plush%20cushion%20and%20durable%20fabric%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20and%20cozy%20design%20with%20professional%20product%20photography%20style&width=300&height=300&seq=106&orientation=squarish"
    },
    {
      id: 7,
      name: "Vitamin Tổng Hợp PetWell",
      price: 220000,
      originalPrice: 250000,
      category: "healthcare",
      petType: "dog",
      brand: "PetWell",
      image: "https://readdy.ai/api/search-image?query=Pet%20vitamin%20supplements%20in%20bottle%20with%20tablets%20spilling%20out%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20of%20the%20supplements%20with%20professional%20pharmaceutical%20photography%20style&width=300&height=300&seq=107&orientation=squarish"
    },
    {
      id: 8,
      name: "Sữa Tắm Dưỡng Lông",
      price: 180000,
      originalPrice: 210000,
      category: "healthcare",
      petType: "dog",
      brand: "Hartz",
      image: "https://readdy.ai/api/search-image?query=Pet%20shampoo%20bottle%20with%20natural%20ingredients%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20of%20the%20bottle%20with%20professional%20product%20photography%20style&width=300&height=300&seq=108&orientation=squarish"
    },
    {
      id: 9,
      name: "Pedigree Adult Vị Bò",
      price: 320000,
      originalPrice: 360000,
      category: "food",
      petType: "dog",
      brand: "Pedigree",
      image: "https://readdy.ai/api/search-image?query=Premium%20beef%20flavored%20dog%20food%20in%20an%20elegant%20packaging%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20of%20the%20packaging%20with%20professional%20product%20photography%20style&width=300&height=300&seq=109&orientation=squarish"
    },
    {
      id: 10,
      name: "Dây Dắt Chó Tự Động",
      price: 190000,
      originalPrice: 230000,
      category: "accessories",
      petType: "dog",
      brand: "Kong",
      image: "https://readdy.ai/api/search-image?query=Retractable%20dog%20leash%20with%20comfortable%20grip%20handle%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20and%20mechanism%20with%20professional%20product%20photography%20style&width=300&height=300&seq=110&orientation=squarish"
    },
    {
      id: 11,
      name: "Nhà Cây Cho Mèo",
      price: 850000,
      originalPrice: 950000,
      category: "accessories",
      petType: "cat",
      brand: "Petstages",
      image: "https://readdy.ai/api/search-image?query=Multi-level%20cat%20tree%20with%20scratching%20posts%20and%20cozy%20platforms%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20and%20design%20with%20professional%20product%20photography%20style&width=300&height=300&seq=111&orientation=squarish"
    },
    {
      id: 12,
      name: "Thuốc Nhỏ Gáy Chó",
      price: 230000,
      originalPrice: 260000,
      category: "healthcare",
      petType: "dog",
      brand: "PetWell",
      image: "https://readdy.ai/api/search-image?query=Flea%20and%20tick%20prevention%20drops%20for%20dogs%20in%20professional%20packaging%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20of%20the%20product%20with%20pharmaceutical%20photography%20style&width=300&height=300&seq=112&orientation=squarish"
    },
    {
      id: 13,
      name: "Hill's Science Diet Mèo",
      price: 380000,
      originalPrice: 420000,
      category: "food",
      petType: "cat",
      brand: "Hill's",
      image: "https://readdy.ai/api/search-image?query=Premium%20cat%20food%20in%20elegant%20packaging%20with%20scientific%20formula%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20of%20the%20packaging%20with%20professional%20product%20photography%20style&width=300&height=300&seq=113&orientation=squarish"
    },
    {
      id: 14,
      name: "Bát Ăn Tự Động",
      price: 320000,
      originalPrice: 380000,
      category: "accessories",
      petType: "cat",
      brand: "Purina",
      image: "https://readdy.ai/api/search-image?query=Automatic%20pet%20food%20dispenser%20with%20modern%20design%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20and%20mechanism%20with%20professional%20product%20photography%20style&width=300&height=300&seq=114&orientation=squarish"
    },
    {
      id: 15,
      name: "Xương Gặm Sạch Răng",
      price: 45000,
      originalPrice: 60000,
      category: "food",
      petType: "dog",
      brand: "Pedigree",
      image: "https://readdy.ai/api/search-image?query=Dental%20chew%20bone%20for%20dogs%20with%20texture%20for%20teeth%20cleaning%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20of%20the%20chew%20bone%20with%20professional%20product%20photography%20style&width=300&height=300&seq=115&orientation=squarish"
    },
    {
      id: 16,
      name: "Cát Vệ Sinh Mèo",
      price: 150000,
      originalPrice: 180000,
      category: "accessories",
      petType: "cat",
      brand: "Purina",
      image: "https://readdy.ai/api/search-image?query=Premium%20clumping%20cat%20litter%20in%20packaging%20with%20some%20litter%20visible%2C%20photographed%20on%20a%20clean%20white%20surface%20with%20subtle%20blue%20background.%20The%20image%20shows%20detailed%20texture%20of%20the%20cat%20litter%20with%20professional%20product%20photography%20style&width=300&height=300&seq=116&orientation=squarish"
    }
  ];

  // Filter products
  const filteredProducts = products.filter(product => {
    // Filter by category
    if (selectedCategory !== "all" && product.category !== selectedCategory) {
      return false;
    }
    
    // Filter by pet type
    if (!selectedPetType.includes(product.petType)) {
      return false;
    }
    
    // Filter by price range
    if (product.price < priceRange[0] || product.price > priceRange[1]) {
      return false;
    }
    
    // Filter by brand
    if (selectedBrands.length > 0 && !selectedBrands.includes(product.brand)) {
      return false;
    }
    
    return true;
  });

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortOption) {
      case "newest":
        return b.id - a.id;
      case "price-asc":
        return a.price - b.price;
      case "price-desc":
        return b.price - a.price;
      case "bestseller":
        return Math.random() - 0.5; // Simulating bestseller sorting
      default:
        return 0;
    }
  });

  // Pagination
  const totalPages = Math.ceil(sortedProducts.length / itemsPerPage);
  const paginatedProducts = sortedProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Main Content */}
      <main className="pt-16">
        {/* Breadcrumb */}
        <div className="bg-gray-100 py-3 border-b border-gray-200">
          <div className="container mx-auto px-4">
            <div className="flex items-center text-sm text-gray-600">
              <a href="#" className="hover:text-blue-600 transition-colors">Trang chủ</a>
              <i className="fas fa-chevron-right text-xs mx-2"></i>
              <span className="text-blue-600">Sản phẩm</span>
            </div>
          </div>
        </div>

        {/* Products Section */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Filters Sidebar */}
              <div className="lg:w-1/4">
                <div className="bg-white rounded-lg shadow-md p-6 sticky top-24">
                  <h2 className="text-xl font-bold mb-6 pb-2 border-b border-gray-200">Bộ lọc sản phẩm</h2>
                  
                  {/* Categories Filter */}
                  <div className="mb-6">
                    <h3 className="font-semibold mb-3">Danh mục sản phẩm</h3>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <input 
                          type="radio" 
                          id="cat-all" 
                          name="category" 
                          checked={selectedCategory === "all"} 
                          onChange={() => handleCategoryChange("all")}
                          className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                        />
                        <label htmlFor="cat-all" className="ml-2 text-gray-700 cursor-pointer">Tất cả sản phẩm</label>
                      </div>
                      <div className="flex items-center">
                        <input 
                          type="radio" 
                          id="cat-food" 
                          name="category" 
                          checked={selectedCategory === "food"} 
                          onChange={() => handleCategoryChange("food")}
                          className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                        />
                        <label htmlFor="cat-food" className="ml-2 text-gray-700 cursor-pointer">Thức ăn</label>
                      </div>
                      <div className="flex items-center">
                        <input 
                          type="radio" 
                          id="cat-accessories" 
                          name="category" 
                          checked={selectedCategory === "accessories"} 
                          onChange={() => handleCategoryChange("accessories")}
                          className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                        />
                        <label htmlFor="cat-accessories" className="ml-2 text-gray-700 cursor-pointer">Phụ kiện</label>
                      </div>
                      <div className="flex items-center">
                        <input 
                          type="radio" 
                          id="cat-healthcare" 
                          name="category" 
                          checked={selectedCategory === "healthcare"} 
                          onChange={() => handleCategoryChange("healthcare")}
                          className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                        />
                        <label htmlFor="cat-healthcare" className="ml-2 text-gray-700 cursor-pointer">Chăm sóc sức khỏe</label>
                      </div>
                    </div>
                  </div>
                  
                  {/* Pet Type Filter */}
                  <div className="mb-6">
                    <h3 className="font-semibold mb-3">Loại thú cưng</h3>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <input 
                          type="checkbox" 
                          id="pet-dog" 
                          checked={selectedPetType.includes("dog")} 
                          onChange={() => handlePetTypeChange("dog")}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="pet-dog" className="ml-2 text-gray-700 cursor-pointer">Chó</label>
                      </div>
                      <div className="flex items-center">
                        <input 
                          type="checkbox" 
                          id="pet-cat" 
                          checked={selectedPetType.includes("cat")} 
                          onChange={() => handlePetTypeChange("cat")}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="pet-cat" className="ml-2 text-gray-700 cursor-pointer">Mèo</label>
                      </div>
                      <div className="flex items-center">
                        <input 
                          type="checkbox" 
                          id="pet-other" 
                          checked={selectedPetType.includes("other")} 
                          onChange={() => handlePetTypeChange("other")}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="pet-other" className="ml-2 text-gray-700 cursor-pointer">Khác</label>
                      </div>
                    </div>
                  </div>
                  
                  {/* Price Range Filter */}
                  <div className="mb-6">
                    <h3 className="font-semibold mb-3">Khoảng giá</h3>
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="min-price" className="block text-sm text-gray-600 mb-1">Giá tối thiểu: {priceRange[0].toLocaleString()} ₫</label>
                        <input 
                          type="range" 
                          id="min-price" 
                          min="0" 
                          max="1000000" 
                          step="10000" 
                          value={priceRange[0]} 
                          onChange={(e) => handlePriceChange(e, 0)}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                      </div>
                      <div>
                        <label htmlFor="max-price" className="block text-sm text-gray-600 mb-1">Giá tối đa: {priceRange[1].toLocaleString()} ₫</label>
                        <input 
                          type="range" 
                          id="max-price" 
                          min="0" 
                          max="1000000" 
                          step="10000" 
                          value={priceRange[1]} 
                          onChange={(e) => handlePriceChange(e, 1)}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* Brands Filter */}
                  <div className="mb-6">
                    <h3 className="font-semibold mb-3">Thương hiệu</h3>
                    <div className="space-y-2 max-h-48 overflow-y-auto pr-2">
                      {brands.map(brand => (
                        <div key={brand} className="flex items-center">
                          <input 
                            type="checkbox" 
                            id={`brand-${brand}`} 
                            checked={selectedBrands.includes(brand)} 
                            onChange={() => handleBrandChange(brand)}
                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <label htmlFor={`brand-${brand}`} className="ml-2 text-gray-700 cursor-pointer">{brand}</label>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Reset Filters Button */}
                  <button 
                    onClick={() => {
                      setSelectedCategory("all");
                      setSelectedPetType(["dog", "cat", "other"]);
                      setPriceRange([0, 1000000]);
                      setSelectedBrands([]);
                      setCurrentPage(1);
                    }}
                    className="w-full bg-gray-200 text-gray-800 py-2 !rounded-button hover:bg-gray-300 transition-colors whitespace-nowrap cursor-pointer"
                  >
                    Xóa bộ lọc
                  </button>
                </div>
              </div>
              
              {/* Products Grid */}
              <div className="lg:w-3/4">
                {/* Sort and View Options */}
                <div className="bg-white rounded-lg shadow-md p-4 mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
                  <div className="flex items-center space-x-4 w-full sm:w-auto">
                    <span className="text-gray-600 whitespace-nowrap">Hiển thị:</span>
                    <select 
                      value={itemsPerPage} 
                      onChange={handleItemsPerPageChange}
                      className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
                    >
                      <option value={12}>12</option>
                      <option value={24}>24</option>
                      <option value={36}>36</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center space-x-4 w-full sm:w-auto">
                    <span className="text-gray-600 whitespace-nowrap">Sắp xếp theo:</span>
                    <select 
                      value={sortOption} 
                      onChange={(e) => handleSortChange(e.target.value)}
                      className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
                    >
                      <option value="newest">Mới nhất</option>
                      <option value="price-asc">Giá tăng dần</option>
                      <option value="price-desc">Giá giảm dần</option>
                      <option value="bestseller">Bán chạy</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button 
                      onClick={() => handleViewModeChange("grid")}
                      className={`p-2 rounded ${viewMode === "grid" ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-600"} cursor-pointer`}
                    >
                      <i className="fas fa-th-large"></i>
                    </button>
                    <button 
                      onClick={() => handleViewModeChange("list")}
                      className={`p-2 rounded ${viewMode === "list" ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-600"} cursor-pointer`}
                    >
                      <i className="fas fa-list"></i>
                    </button>
                  </div>
                </div>
                
                {/* Results Count */}
                <div className="mb-6">
                  <p className="text-gray-600">
                    Hiển thị {paginatedProducts.length} trên {filteredProducts.length} sản phẩm
                  </p>
                </div>
                
                {/* Products Display */}
                {paginatedProducts.length === 0 ? (
                  <div className="bg-white rounded-lg shadow-md p-8 text-center">
                    <i className="fas fa-search text-gray-400 text-5xl mb-4"></i>
                    <h3 className="text-xl font-semibold mb-2">Không tìm thấy sản phẩm</h3>
                    <p className="text-gray-600 mb-4">Không có sản phẩm nào phù hợp với bộ lọc của bạn.</p>
                    <button 
                      onClick={() => {
                        setSelectedCategory("all");
                        setSelectedPetType(["dog", "cat", "other"]);
                        setPriceRange([0, 1000000]);
                        setSelectedBrands([]);
                      }}
                      className="bg-blue-600 text-white px-4 py-2 !rounded-button hover:bg-blue-700 transition-colors whitespace-nowrap cursor-pointer"
                    >
                      Xóa bộ lọc
                    </button>
                  </div>
                ) : (
                  <div className={viewMode === "grid" ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" : "space-y-6"}>
                    {paginatedProducts.map(product => (
                      viewMode === "grid" ? (
                        <div key={product.id} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                          <div className="h-64 overflow-hidden">
                            <img
                              src={product.image}
                              alt={product.name}
                              className="w-full h-full object-cover object-top hover:scale-105 transition-transform duration-300"
                            />
                          </div>
                          <div className="p-4">
                            <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
                            <div className="flex justify-between items-center mb-4">
                              <span className="text-blue-600 font-bold">{product.price.toLocaleString()} ₫</span>
                              <span className="text-gray-500 line-through text-sm">{product.originalPrice.toLocaleString()} ₫</span>
                            </div>
                            <button
                              onClick={addToCart}
                              className="w-full bg-blue-600 text-white py-2 !rounded-button hover:bg-blue-700 transition-colors flex items-center justify-center whitespace-nowrap cursor-pointer"
                            >
                              <i className="fas fa-shopping-cart mr-2"></i>
                              Thêm vào giỏ
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div key={product.id} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow flex flex-col sm:flex-row">
                          <div className="sm:w-1/3 h-64 sm:h-auto overflow-hidden">
                            <img
                              src={product.image}
                              alt={product.name}
                              className="w-full h-full object-cover object-top hover:scale-105 transition-transform duration-300"
                            />
                          </div>
                          <div className="sm:w-2/3 p-4 flex flex-col justify-between">
                            <div>
                              <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
                              <p className="text-gray-600 mb-4">Thương hiệu: {product.brand}</p>
                              <div className="flex items-center mb-4">
                                <span className="text-blue-600 font-bold text-xl mr-3">{product.price.toLocaleString()} ₫</span>
                                <span className="text-gray-500 line-through">{product.originalPrice.toLocaleString()} ₫</span>
                              </div>
                            </div>
                            <button
                              onClick={addToCart}
                              className="bg-blue-600 text-white py-2 px-4 !rounded-button hover:bg-blue-700 transition-colors flex items-center justify-center sm:w-1/2 whitespace-nowrap cursor-pointer"
                            >
                              <i className="fas fa-shopping-cart mr-2"></i>
                              Thêm vào giỏ
                            </button>
                          </div>
                        </div>
                      )
                    ))}
                  </div>
                )}
                
                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-8 flex justify-center">
                    <div className="flex items-center space-x-1">
                      <button 
                        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className={`px-3 py-1 rounded-l-lg ${currentPage === 1 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300 cursor-pointer'}`}
                      >
                        <i className="fas fa-chevron-left"></i>
                      </button>
                      
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-3 py-1 ${currentPage === page ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'} cursor-pointer`}
                        >
                          {page}
                        </button>
                      ))}
                      
                      <button 
                        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className={`px-3 py-1 rounded-r-lg ${currentPage === totalPages ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300 cursor-pointer'}`}
                      >
                        <i className="fas fa-chevron-right"></i>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>
      </main>


    </div>
  );
}

export default ProductList;

