import React, { useState, useEffect } from 'react';
import addressData from '../../data/address.json';

interface AddressCardProps {
  province: string;
  district: string;
  ward: string;
  hamlet: string;
  address: string;
  postalCode?: string;
  isEditMode: boolean;
  onChange: (field: string, value: string) => void;
}

interface District {
  name: string;
  ward: string[];
}

interface Province {
  name: string;
  district: District[];
}

const AddressCard: React.FC<AddressCardProps> = ({
                                                   province,
                                                   district,
                                                   ward,
                                                   hamlet,
                                                   address,
                                                   postalCode,
                                                   isEditMode,
                                                   onChange,
                                                 }) => {
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [wards, setWards] = useState<string[]>([]);

  // Load provinces
  useEffect(() => {
    if (addressData.province) {
      setProvinces(addressData.province);
    }
  }, []);

  // Update districts when province changes
  useEffect(() => {
    const selectedProvince = provinces.find((p) => p.name === province);
    const newDistricts = selectedProvince ? selectedProvince.district : [];
    setDistricts(newDistricts);
    if (district && !newDistricts.some((d) => d.name === district)) {
      onChange('district', '');
      onChange('ward', '');
    }
    setWards([]);
  }, [province, provinces, district, onChange]);

  // Update wards when district changes
  useEffect(() => {
    const selectedDistrict = districts.find((d) => d.name === district);
    const newWards = selectedDistrict ? selectedDistrict.ward : [];
    setWards(newWards);
    if (ward && !newWards.includes(ward)) {
      onChange('ward', '');
    }
  }, [district, districts, ward, onChange]);

  const fields = [
    {
      label: 'Tỉnh/Thành phố',
      key: 'province',
      type: 'select',
      options: provinces.map((p) => ({ value: p.name, label: p.name })),
    },
    {
      label: 'Quận/Huyện',
      key: 'district',
      type: 'select',
      options: districts.map((d) => ({ value: d.name, label: d.name })),
    },
    {
      label: 'Phường/Xã',
      key: 'ward',
      type: 'select',
      options: wards.map((w) => ({ value: w, label: w })),
    },
    { label: 'Thôn/Xóm', key: 'hamlet', type: 'text' },
    { label: 'Địa chỉ', key: 'address', type: 'text' },
    { label: 'Mã bưu điện', key: 'postalCode', type: 'text' },
  ];

  return (
      <div className="space-y-4">
        <h4 className="text-lg font-medium text-gray-800">Địa chỉ</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {fields.map(({ label, key, type, options }) => (
              <div key={key}>
                <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
                {isEditMode ? (
                    type === 'select' ? (
                        <select
                            value={
                              key === 'province' ? province : key === 'district' ? district : ward
                            }
                            onChange={(e) => onChange(key, e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary"
                            aria-label={label}
                            disabled={key !== 'province' && !province}
                        >
                          <option value="">Chọn {label}</option>
                          {options?.map((opt) => (
                              <option key={opt.value} value={opt.value}>
                                {opt.label}
                              </option>
                          ))}
                        </select>
                    ) : (
                        <input
                            type="text"
                            value={
                              key === 'hamlet' ? hamlet : key === 'address' ? address : postalCode || ''
                            }
                            onChange={(e) => onChange(key, e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary"
                            aria-label={label}
                        />
                    )
                ) : (
                    <p className="py-2 px-4 bg-gray-50 rounded">
                      {(key === 'province'
                          ? province
                          : key === 'district'
                              ? district
                              : key === 'ward'
                                  ? ward
                                  : key === 'hamlet'
                                      ? hamlet
                                      : key === 'address'
                                          ? address
                                          : postalCode) || 'Chưa cập nhật'}
                    </p>
                )}
              </div>
          ))}
        </div>
      </div>
  );
};

export default AddressCard;