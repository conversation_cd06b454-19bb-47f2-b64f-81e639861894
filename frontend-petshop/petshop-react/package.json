{"name": "petshop-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@heroicons/react": "^2.2.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "firebase": "^11.8.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.511.0", "petshop-react": "file:", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.2.7", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}